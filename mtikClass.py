import routeros_api
from routeros_api.exceptions import RouterOsApiCommunicationError
import re




#
# Mikrotik functions
# https://pypi.org/project/RouterOS-api/
# https://github.com/socialwifi/RouterOS-api
#
class mtikClass:
    def router_connect(self, ip):
        if ip is None:
            return None, "No IP address"
        try:
            connection = routeros_api.RouterOsApiPool(
                ip,
                username='admin',
                password='3SHOREn3t',
                plaintext_login=True,
                use_ssl=False
            )
            api = connection.get_api()
        except RouterOsApiCommunicationError as e:
            return None, f"RouterOS API Communication Error: {str(e)}"
        except Exception as e:
            return None, f"Connection Error: {str(e)}"

        return api, "Success"

    def router_get_resource(self, ipaddress, resource):
        api = None
        api_result = self.router_connect(ipaddress)
        if not api_result[0]:
            return api_result[1]
        api = api_result[0]
        list_resources = api.get_resource(resource)
        return list_resources.get()

    def get_router_hostname(self, ipaddress):
        api = None
        api_result = self.router_connect(ipaddress)
        if not api_result[0]:
            return api_result[1]
        api = api_result[0]
        list_resource = api.get_resource('/system/identity')
        list_identity = list_resource.get()
        return list_identity[0]['name']

    def get_router_info(self, ipaddress):
        api = None
        api_result = self.router_connect(ipaddress)
        if not api_result[0]:
            return api_result[1]
        api = api_result[0]
        list_resource = api.get_resource('/system/identity')
        list_identity = list_resource.get()
        hostname = list_identity[0]['name']
        list_resource = api.get_resource('/system/routerboard')
        list_routerboard = list_resource.get()
        model = list_routerboard[0]['model']
        current_firmware = list_routerboard[0]['current-firmware']
        list_resource = api.get_resource('/interface/wireless')
        list_wireless_interfaces = list_resource.get()
        has_rental = False
        ssid_5 = ''
        freq_5 = 0
        for wireless_interface in list_wireless_interfaces:
            if (wireless_interface['ssid'][:6] == "Rental"):
                has_rental = True
            else:
                if (wireless_interface['band'][:4] == "2ghz"):
                    ssid_24 = wireless_interface['ssid']
                    freq_24 = wireless_interface['frequency']
                elif (wireless_interface['band'][:4] == "5ghz"):
                    ssid_5 = wireless_interface['ssid']
                    freq_5 = wireless_interface['frequency']
        list_resource = api.get_resource('/interface/wireless/security-profiles')
        list_security_profile = list_resource.get(name='default')
        wireless_password = list_security_profile[0]['wpa-pre-shared-key']
        list_resource = api.get_resource('/interface/wireless/registration-table')
        wireless_clients = len(list_resource.get())
        list_resource = api.get_resource('/queue/simple')
        list_queue = list_resource.get()
        queue_name = list_queue[0]['name']
        result = ({
            'hostname': hostname, 'model': model, 'firmware': current_firmware, 'rental': has_rental,
            'ssid_24': ssid_24, 'freq_24': freq_24, 'freq_5': freq_5, 'ssid_5': ssid_5, 'ssid_pwd': wireless_password,
            'ssid_clients': wireless_clients,
            'queue_name': queue_name, 'ipaddress': ipaddress
        })
        return result

    def get_ssid(self, ipaddress):
        ssids = self.router_get_resource(ipaddress, '/interface/wireless')
        pwds = self.router_get_resource(ipaddress, '/interface/wireless/security-profiles')
        result = []
        for ssid in ssids:
            for pwd in pwds:
                if ssid['security-profile'] == pwd['name']:
                    password = pwd['wpa2-pre-shared-key']
                    break
            if 'master-interface' in ssid:
                result.append({
                    'ssid': ssid['ssid'],
                    'password': password,
                    'master-interface': ssid['master-interface'],
                    'security-profile': ssid['security-profile'],
                    'mac-address': ssid['mac-address'],
                    'band': 'na',
                    'channel-width': 'na',
                    'frequency': 'na',
                })
            else:
                result.append({
                    'ssid': ssid['ssid'],
                    'password': password,
                    'master-interface': ssid['name'],
                    'security-profile': ssid['security-profile'],
                    'mac-address': ssid['mac-address'],
                    'band': ssid['band'],
                    'channel-width': ssid['channel-width'],
                    'frequency': ssid['frequency'],
                })
        return result

    def get_registration_list(self, ipaddress):
        registrations = self.router_get_resource(ipaddress, '/interface/wireless/registration-table')
        return registrations

    def get_queue(self, ipaddress):
        queues = self.router_get_resource(ipaddress, '/queue/simple')
        return queues

    def add_queue(self, ipaddress, queue):
        # list_queues.add(name="001", max_limit="512k/4M", target="************/32")
        api = None
        api_result = self.router_connect(ipaddress)
        if not api_result[0]:
            return api_result[1]
        api = api_result[0]
        list_queues = api.get_resource('/queue/simple')
        # delete existing queues
        old_queues = list_queues.get()
        for old_queue in old_queues:
            list_queues.remove(id=old_queue['id'])
        list_queues.add(name=queue['name'], max_limit=queue['max_limit'], target=queue['target'], queue=queue['queue'],
                        burst_limit=queue['burst_limit'], burst_threshold=queue['burst_threshold'], dst=queue['dst'],
                        burst_time=queue['burst_time'])
        return True

    def remove_interface(self, resource, interface):
        found = resource.get(interface=interface)
        if not found:
            return interface + " Not Found"
        else:
            resource.remove(id=found[0]['id'])
        return interface + " Removed"

    def remove_resource(self, resource, name):
        found = resource.get(name=name)
        if not found:
            return name + " Not Found"
        else:
            resource.remove(id=found[0]['id'])
        return name + " Removed"

    def reset_ap(self, ipaddress):
        #ipaddress can also be "unit"
        ip_pattern = re.compile(r'^(\d{1,3}\.){3}\d{1,3}$')
        isValidIP = bool(ip_pattern.match(str(ipaddress)))

        # Import SQLiteClass only when needed to avoid circular imports
        from SQLiteClass import SQLiteClass
        db = SQLiteClass()

        if not isValidIP:
            ipaddress = db.get_unit_router_ip(ipaddress)
            if ipaddress is None:
                return "Unit not found"

        api = None
        api_result = self.router_connect(ipaddress)
        if not api_result[0]:
            return api_result[1]
        api = api_result[0]
        result = []
        # 1 Remove Rental security profile
        resource = api.get_resource('/interface/wireless/security-profiles')
        result.append('Security Profile: ' + self.remove_resource(resource, 'Rental'))
        resource = api.get_resource('/interface/bridge/port')
        result.append('Bridge: ' + self.remove_interface(resource, 'Rental'))
        result.append('Bridge: ' + self.remove_interface(resource, 'Rental5G'))
        resource = api.get_resource('/interface/wireless')
        result.append('Wireless: ' + self.remove_resource(resource, 'Rental'))
        result.append('Wireless: ' + self.remove_resource(resource, 'Rental5G'))
        resource = api.get_resource('/queue/simple')
        result.append('Queue: ' + self.remove_resource(resource, 'Rental'))

        turn_off_result = db.router_turn_rental_off(ipaddress)

        if turn_off_result == 0:
            result.append('Database: Rental status not updated')
        else:
            expiry_result = db.expire_active_network(ipaddress)

        return result

    def new_rental(self, ipaddress, unit, password, package):
        print("mtikClass.new_rental")
        api = None
        api_result = self.router_connect(ipaddress)
        print(f"api_result", api_result)
        if not api_result[0]:
            return api_result[1]
        api = api_result[0]
        print("before get resource")
        # 1 add security profile
        list_secpro = api.get_resource('/interface/wireless/security-profiles')
        security_profiles = list_secpro.get()
        print("before secpro.add")
        try:
            list_secpro.add(name='Rental', wpa_pre_shared_key=password, wpa2_pre_shared_key=password,
                            mode='dynamic-keys', authentication_types='wpa-psk,wpa2-psk')
        except RouterOsApiCommunicationError as e:
            if "profile with the same name already exists" in str(e):
                return False, "Security profile 'Rental' already exists. Skipping creation."
        # Re-raise other unexpected errors
        list_wifi = api.get_resource('/interface/wireless')
        ssids = list_wifi.get()
        list_bridge_ports = api.get_resource('/interface/bridge/port')
        for ssid in ssids:
            if (ssid['name'] == 'wlan1'):
                list_wifi.add(name='Rental', ssid='Rental' + unit, security_profile='Rental', master_interface='wlan1',
                              disabled='no')
                list_bridge_ports.add(interface='Rental', bridge='bridge')
            elif (ssid['name'] == 'wlan2'):
                list_wifi.add(name='Rental5G', ssid='Rental' + unit, security_profile='Rental',
                              master_interface='wlan2', disabled='no')
                list_bridge_ports.add(interface='Rental5G', bridge='bridge')
        # 3 add queue based on packages. Basic 25/15 Streaming 50/15
        list_queue = api.get_resource('/queue/simple')
        queues = list_queue.get()
        if package == 'standard':
            list_queue.add(name='Rental', place_before='0', target='************/24', dst='ether1', max_limit='15M/25M',
                           burst_threshold='15M/25M', burst_limit='20M/30M', burst_time='5s/5s')
        elif package == 'streaming':
            list_queue.add(name='Rental', place_before='0', target='************/24', dst='ether1', max_limit='15M/50M',
                           burst_threshold='15M/50M', burst_limit='20M/60M', burst_time='5s/5s')
        return True, "Success."

    def security_profile_exists(self, ipaddress, profile_name='Rental'):
        """
        Check if a security profile exists on the router

        Args:
            ipaddress: Router IP address
            profile_name: Name of the security profile to check

        Returns:
            bool: True if profile exists, False otherwise
        """

        print(f"Checking if profile '{profile_name}' exists on {ipaddress}")
        api_result = self.router_connect(ipaddress)
        if not api_result[0]:
            print(f"Connection failed: {api_result[1]}")
            return False

        api = api_result[0]
        list_secpro = api.get_resource('/interface/wireless/security-profiles')
        security_profiles = list_secpro.get(name=profile_name)
        
        exists = len(security_profiles) > 0
        password = ""

        if exists:
            profile_entry = security_profiles[0]
            password = profile_entry.get('wpa2-pre-shared-key') \
                   or profile_entry.get('wpa-pre-shared-key') \
                   or ''

        print(f"Profile check result: {exists}, found profiles: {security_profiles}")
        return exists, password

    def get_router_profile_information(self, ipaddress, profile_name='Rental'):
        """
        Check if a given wireless security-profile exists on the router.
        If it does, return (True, password, package), where:
          - password is the WPA2 pre-shared key
          - package is 'standard' or 'streaming' (based on the existing queue limits)

        If the profile does not exist, return (False, None, None).
        If any connection error occurs, return (False, error_message, None).
        """
        # 1) Connect to the router
        api_result = self.router_connect(ipaddress)
        if not api_result[0]:
            # router_connect returned (None, "<error>"), so propagate that message
            return False, api_result[1], None

        api = api_result[0]

        # 2) Look for the security-profile named profile_name
        secpro_res = api.get_resource('/interface/wireless/security-profiles')
        try:
            profiles = secpro_res.get(name=profile_name)
        except Exception as e:
            return False, f"Error fetching security-profiles: {e}", None

        if not profiles:
            # No profile with that name
            return False, None, None

        # Extract the password (wpa2-pre-shared-key if available, else wpa-pre-shared-key)
        profile_entry = profiles[0]
        password = profile_entry.get('wpa2-pre-shared-key') \
                   or profile_entry.get('wpa-pre-shared-key') \
                   or ''

        # 3) Determine the package type by checking queue limits
        package = 'standard'  # default
        try:
            queue_res = api.get_resource('/queue/simple')
            queues = queue_res.get(name=profile_name)
            if queues:
                queue_entry = queues[0]
                max_limit = queue_entry.get('max-limit', '')
                # Check if it's streaming package (50M upload) vs standard (25M upload)
                if '50M' in max_limit:
                    package = 'streaming'
                elif '25M' in max_limit:
                    package = 'standard'
        except Exception as e:
            # If we can't determine package, default to standard
            pass

        return True, password, package
